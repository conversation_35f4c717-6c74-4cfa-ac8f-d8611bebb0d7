/*
 * APITable <https://github.com/apitable/apitable>
 * Copyright (C) 2022 APITable Ltd. <https://apitable.com>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.apitable.internal.controller;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import com.apitable.asset.enums.AssetType;
import com.apitable.asset.service.IAssetCallbackService;
import com.apitable.asset.service.IAssetUploadTokenService;
import com.apitable.asset.vo.AssetUploadCertificateVO;
import com.apitable.asset.vo.AssetUploadResult;
import com.apitable.shared.component.scanner.annotation.ApiResource;
import com.apitable.shared.component.scanner.annotation.GetResource;
import com.apitable.shared.context.SessionContext;
import com.apitable.core.support.ResponseData;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 内部服务 - 资源附件API控制器
 * 为内部微服务提供资源附件相关的API接口，包括获取上传凭证和资源信息查询
 *
 * Internal Server - Asset API
 *
 * <AUTHOR>
 * @date 2022/8/20
 */
@RestController
@ApiResource(path = "/internal/asset")
@Api(tags = "Internal Server - Asset API")
public class InternalAssetController {

    @Resource
    private IAssetUploadTokenService iAssetUploadTokenService;

    @Resource
    private IAssetCallbackService iAssetCallbackService;

    /**
     * 获取空间资源上传预签名URL
     * 为指定节点生成数据表类型的资源上传凭证，支持批量创建（最多20个）
     *
     * Get Upload PreSigned URL
     * @param nodeId 节点自定义ID，必须提供
     * @param count 创建数量（默认1，最大3个）
     * @return 上传凭证列表，包含预签名URL和相关参数
     */
    @GetResource(path = "/upload/preSignedUrl", requiredPermission = false)
    @ApiOperation(value = "Get Upload PreSigned URL")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "nodeId", value = "node custom id", required = true, dataTypeClass = String.class, paramType = "query", example = "dst123"),
            @ApiImplicitParam(name = "count", value = "number to create (default 1, max 20)", dataTypeClass = String.class, paramType = "query", example = "2")
    })
    public ResponseData<List<AssetUploadCertificateVO>> getSpaceCapacity(@RequestParam("nodeId") String nodeId, @RequestParam(name = "count", defaultValue = "1") Integer count) {
        Long userId = SessionContext.getUserId();
        return ResponseData.success(iAssetUploadTokenService.createSpaceAssetPreSignedUrl(userId, nodeId, AssetType.DATASHEET.getValue(), count));
    }

    /**
     * 获取资源信息
     * 根据资源令牌查询资源信息，主要用于融合服务器在写入数据前查询附件字段数据
     *
     * Get Asset Info
     * @param token 资源键，用于标识特定的资源
     * @return 资源上传结果，包含资源URL、尺寸等信息
     */
    @GetResource(name = "Get Asset Info", path = "/get", requiredLogin = false)
    @ApiOperation(value = "Get Asset Info", notes = "scene：Fusion server query the attachment field data before writing")
    @ApiImplicitParam(name = "token", value = "resource key", required = true, dataTypeClass = String.class, paramType = "query", example = "space/2019/12/10/159")
    public ResponseData<AssetUploadResult> get(@RequestParam("token") String token) {
        // load asset upload result
        List<AssetUploadResult> results = iAssetCallbackService.loadAssetUploadResult(AssetType.DATASHEET, Collections.singletonList(token));
        return ResponseData.success(results.stream().findFirst().orElse(null));
    }
}
