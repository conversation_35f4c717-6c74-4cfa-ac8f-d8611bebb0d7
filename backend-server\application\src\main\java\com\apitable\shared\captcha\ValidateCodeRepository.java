/*
 * APITable <https://github.com/apitable/apitable>
 * Copyright (C) 2022 APITable Ltd. <https://apitable.com>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.apitable.shared.captcha;

/**
 * <p>
 * verification code access interface
 * </p>
 *
 * <AUTHOR>
 */
public interface ValidateCodeRepository {

    /**
     * save verification code
     *
     * @param type          verification code type
     * @param code          verification code information
     * @param target        verification object mobile phone or email
     * @param effectiveTime valid time(unit: minutes)
     */
    void save(String type, ValidateCode code, String target, int effectiveTime);

    /**
     * get verification code
     *
     * @param type   verification code type
     * @param target verification object mobile phone or email
     * @param scope  verification code scope
     * @return ValidateCode
     */
    ValidateCode get(String target, ValidateCodeType type, String scope);

    /**
     * remove verification code
     *
     * @param type   verification code type
     * @param target verification object mobile phone or email
     * @param scope  verification code scope
     */
    void remove(String target, ValidateCodeType type, String scope);
}
