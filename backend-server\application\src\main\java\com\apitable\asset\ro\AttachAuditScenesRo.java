/*
 * APITable <https://github.com/apitable/apitable>
 * Copyright (C) 2022 APITable Ltd. <https://apitable.com>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.apitable.asset.ro;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <p>
 * Image audit result request parameters
 * </p>
 */
@Data
@ApiModel("Image audit result request parameters")
public class AttachAuditScenesRo {

	@ApiModelProperty(value = "Audit results of image sensitive persons", position = 1, required = true)
	@NotNull(message = "Yellow identification results of pictures")
	private String politician;

	@ApiModelProperty(value = "Photo Yellow Identification Review Results", position = 2, required = true)
	@NotNull(message = "Yellow identification results of pictures")
	private AttachAuditPulpResultRo pulp;


	@ApiModelProperty(value = "Audit Results of Photo Violence", position = 3, required = true)
	@NotNull(message = "Audit Results of Photo Violence")
	private String terror;
}
