/*
 * APITable <https://github.com/apitable/apitable>
 * Copyright (C) 2022 APITable Ltd. <https://apitable.com>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.apitable.interfaces.social.event;

public class TemplateQuoteEvent implements SocialEvent {

    private String spaceId;

    private String nodeId;

    private String templateId;

    private Long memberId;

    public TemplateQuoteEvent(String spaceId, String nodeId, String templateId, Long memberId) {
        this.spaceId = spaceId;
        this.nodeId = nodeId;
        this.templateId = templateId;
        this.memberId = memberId;
    }

    @Override
    public CallEventType getEventType() {
        return CallEventType.TEMPLATE_QUOTE;
    }

    public String getSpaceId() {
        return spaceId;
    }

    public String getNodeId() {
        return nodeId;
    }

    public String getTemplateId() {
        return templateId;
    }

    public Long getMemberId() {
        return memberId;
    }
}
