/*
 * APITable <https://github.com/apitable/apitable>
 * Copyright (C) 2022 APITable Ltd. <https://apitable.com>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.apitable.shared.cache.service;

/**
 * <p>
 * user active space interface
 * </p>
 *
 * <AUTHOR>
 */
public interface UserActiveSpaceCacheService {

    /**
     * Cache the space latest worked user stayed
     *
     * @param userId  user id
     * @param spaceId space id
     */
    void save(Long userId, String spaceId);

    /**
     * get the space latest worked user stayed
     *
     * @param userId user id
     * @return space id
     */
    String getLastActiveSpace(Long userId);

    /**
     * delete cache
     *
     * @param userId user id
     */
    void delete(Long userId);
}
