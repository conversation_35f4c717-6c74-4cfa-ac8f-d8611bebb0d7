/*
 * APITable <https://github.com/apitable/apitable>
 * Copyright (C) 2022 APITable Ltd. <https://apitable.com>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.apitable.organization.controller;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import cn.hutool.core.lang.Dict;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import com.apitable.shared.component.scanner.annotation.ApiResource;
import com.apitable.shared.component.scanner.annotation.GetResource;
import com.apitable.shared.component.notification.annotation.Notification;
import com.apitable.shared.util.page.PageObjectParam;
import com.apitable.shared.component.scanner.annotation.PostResource;
import com.apitable.shared.component.notification.NotificationRenderField;
import com.apitable.shared.component.notification.NotificationTemplateId;
import com.apitable.shared.constants.ParamsConstants;
import com.apitable.shared.context.LoginContext;
import com.apitable.shared.util.page.PageHelper;
import com.apitable.shared.holder.NotificationRenderFieldHolder;
import com.apitable.shared.holder.UserHolder;
import com.apitable.shared.util.page.PageInfo;
import com.apitable.organization.ro.AddRoleMemberRo;
import com.apitable.organization.ro.CreateRoleRo;
import com.apitable.organization.ro.DeleteRoleMemberRo;
import com.apitable.organization.ro.UpdateRoleRo;
import com.apitable.organization.vo.RoleInfoVo;
import com.apitable.organization.vo.RoleMemberVo;
import com.apitable.organization.service.IRoleMemberService;
import com.apitable.organization.service.IRoleService;
import com.apitable.space.service.ISpaceService;
import com.apitable.core.support.ResponseData;
import com.apitable.core.util.ExceptionUtil;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import static com.apitable.shared.constants.NotificationConstants.ROLE_NAME;
import static com.apitable.shared.constants.PageConstants.PAGE_PARAM;
import static com.apitable.shared.constants.PageConstants.PAGE_SIMPLE_EXAMPLE;
import static com.apitable.organization.enums.OrganizationException.DUPLICATION_ROLE_NAME;
import static com.apitable.organization.enums.OrganizationException.NOT_EXIST_ROLE;
import static com.apitable.organization.enums.OrganizationException.ROLE_EXIST_ROLE_MEMBER;
import static com.apitable.organization.enums.OrganizationException.SPACE_EXIST_ROLES;
import static com.apitable.workspace.enums.PermissionException.MEMBER_NOT_IN_SPACE;

/**
 * 组织角色控制器 - 管理空间内角色的创建、编辑、删除及成员关系管理
 * 提供角色体系的完整管理功能，包括角色信息维护和成员权限分配
 * 支持角色成员的批量管理和初始化角色设置
 * 
 * Contacts Role Controller - Manages role creation, editing, deletion and member relationship management within space
 */
@RestController
@Api(tags = "Contacts Role Api")
@ApiResource(path = "/org")
@Slf4j
public class RoleController {

    @Resource
    IRoleService iRoleService;

    @Resource
    IRoleMemberService iRoleMemberService;

    @Resource
    ISpaceService iSpaceService;

    /**
     * 创建新角色 - 在空间中创建一个新的角色
     * 需要检查角色名称的唯一性，避免重名冲突
     * 创建成功后可用于成员权限管理和组织架构细化管理
     * @param data 角色创建信息，包含角色名称
     * @return 无返回数据，操作成功即可
     */
    @PostResource(path = "/roles", name = "create role", tags = "CREATE_ROLE")
    @ApiOperation(value = "create new role", notes = "create new role", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams({
            @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", required = true, dataTypeClass = String.class, paramType = "header", example = "spcyQkKp9XJEl")
    })
    public ResponseData<Void> createRole(@RequestBody @Valid CreateRoleRo data) {
        String spaceId = LoginContext.me().getSpaceId();
        // check if exist the same role name.
        iRoleService.checkDuplicationRoleName(spaceId, data.getRoleName(),
                status -> ExceptionUtil.isFalse(status, DUPLICATION_ROLE_NAME));
        Long userId = UserHolder.get();
        // add the role.
        iRoleService.createRole(userId, spaceId, data.getRoleName());
        return ResponseData.success();
    }

    /**
     * 更新角色信息 - 修改指定角色的基本信息
     * 支持修改角色名称，同样需要检查名称的唯一性
     * 更新操作需要相应的角色管理权限
     * @param roleId 角色ID
     * @param data 角色信息更新数据，包含新的角色名称
     * @return 无返回数据，操作成功即可
     */
    @PostResource(path = "/roles/{roleId}", method = RequestMethod.PATCH, name = "update role information", tags = "UPDATE_ROLE")
    @ApiOperation(value = "update role information", notes = "update role information", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams({
            @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", required = true, dataTypeClass = String.class, paramType = "header", example = "spcyQkKp9XJEl")
    })
    public ResponseData<Void> updateRole(@PathVariable("roleId") Long roleId, @RequestBody @Valid UpdateRoleRo data) {
        String spaceId = LoginContext.me().getSpaceId();
        // check if exist the same role name.
        iRoleService.checkDuplicationRoleName(spaceId, data.getRoleName(),
                status -> ExceptionUtil.isFalse(status, DUPLICATION_ROLE_NAME));
        Long userId = UserHolder.get();
        // update role information.
        iRoleService.updateRole(userId, roleId, data.getRoleName());
        return ResponseData.success();
    }

    /**
     * 查询空间角色列表 - 获取当前空间中的所有角色信息
     * 返回空间内所有可用角色的基本信息，用于管理界面显示
     * 仅空间成员可以查看角色列表
     * @return 角色信息列表，包含角色ID、名称等基本信息
     */
    @GetResource(path = "/roles", name = "query space's roles")
    @ApiOperation(value = "query roles", notes = "query the space's roles", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams({
            @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", required = true, dataTypeClass = String.class, paramType = "header", example = "spcyQkKp9XJEl")
    })
    public ResponseData<List<RoleInfoVo>> getRoles() {
        String spaceId = LoginContext.me().getSpaceId();
        // check if user in the space.
        Long userId = UserHolder.get();
        iSpaceService.checkUserInSpace(userId, spaceId,
                status -> ExceptionUtil.isTrue(status, MEMBER_NOT_IN_SPACE));
        // get the space's roles.
        return ResponseData.success(iRoleService.getRoles(spaceId));
    }

    /**
     * 查询角色成员列表 - 分页查询指定角色下的所有成员信息
     * 返回角色成员的详细信息，支持分页显示以提高性能
     * 需要验证用户在空间中的成员身份和角色存在性
     * @param roleId 角色ID
     * @param page 分页参数
     * @return 分页的角色成员信息列表
     */
    @GetResource(path = "/roles/{roleId}/members", name = "query role's members")
    @ApiOperation(value = "query role members", notes = "query the role's members", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams({
            @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", required = true, dataTypeClass = String.class, paramType = "header", example = "spcyQkKp9XJEl"),
            @ApiImplicitParam(name = PAGE_PARAM, value = "page parameters", required = true, dataTypeClass = String.class, paramType = "query", example = PAGE_SIMPLE_EXAMPLE)
    })
    public ResponseData<PageInfo<RoleMemberVo>> getRoleMembers(@PathVariable("roleId") Long roleId, @PageObjectParam Page<Void> page) {
        String spaceId = LoginContext.me().getSpaceId();
        // check if user in the space.
        Long userId = UserHolder.get();
        iSpaceService.checkUserInSpace(userId, spaceId,
                status -> ExceptionUtil.isTrue(status, MEMBER_NOT_IN_SPACE));
        // check if space has the role.
        iRoleService.checkRoleExistBySpaceIdAndRoleId(spaceId, roleId,
                status -> ExceptionUtil.isTrue(status, NOT_EXIST_ROLE));
        IPage<RoleMemberVo> resultPage = iRoleMemberService.getRoleMembersPage(spaceId, roleId, page);
        return ResponseData.success(PageHelper.build(resultPage));
    }

    /**
     * 添加角色成员 - 将指定的组织单元（成员/团队）添加到角色中
     * 支持批量添加多个成员或团队到指定角色
     * 添加成功后会向相关成员发送角色分配通知
     * 注意：不过滤角色中已存在的团队成员，可能导致重复通知
     * @param roleId 角色ID
     * @param data 添加成员信息，包含组织单元ID列表
     * @return 无返回数据，操作成功即可
     */
    @PostResource(path = "/roles/{roleId}/members", name = "add role members", tags = "ADD_ROLE_MEMBER")
    @Notification(templateId = NotificationTemplateId.ASSIGNED_TO_ROLE)
    @ApiOperation(value = "add role members", notes = "add role members", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams({
            @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", required = true, dataTypeClass = String.class, paramType = "header", example = "spcyQkKp9XJEl")
    })
    public ResponseData<Void> addRoleMembers(@PathVariable("roleId") Long roleId, @RequestBody @Valid AddRoleMemberRo data) {
        String spaceId = LoginContext.me().getSpaceId();
        // check if space has the role.
        iRoleService.checkRoleExistBySpaceIdAndRoleId(spaceId, roleId,
                status -> ExceptionUtil.isTrue(status, NOT_EXIST_ROLE));
        // add role members.
        List<Long> memberIds = iRoleMemberService.addRoleMembers(roleId, data.getUnitList());
        // send the message being added to the role to the members.
        // ps: it no filter the role exist teams' member. so member may be notified repeatedly.
        NotificationRenderFieldHolder.set(NotificationRenderField.builder().playerIds(memberIds).bodyExtras(
                Dict.create().set(ROLE_NAME, iRoleService.getRoleNameByRoleId(roleId))).build());
        return ResponseData.success();
    }

    /**
     * 移除角色成员 - 从指定角色中移除指定的成员或团队
     * 支持批量移除多个组织单元，只影响角色成员身份不影响空间成员身份
     * 移除成功后会向相关成员发送角色移除通知
     * 注意：不过滤角色中已存在的团队成员，可能导致重复通知
     * @param roleId 角色ID
     * @param data 移除成员信息，包含组织单元ID列表
     * @return 无返回数据，操作成功即可
     */
    @PostResource(path = "/roles/{roleId}/members", method = RequestMethod.DELETE, name = "remove role members", tags = "REMOVE_ROLE_MEMBER")
    @ApiOperation(value = "remove role members", notes = "remove role members", produces = MediaType.APPLICATION_JSON_VALUE)
    @Notification(templateId = NotificationTemplateId.REMOVED_FROM_ROLE)
    @ApiImplicitParams({
            @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", required = true, dataTypeClass = String.class, paramType = "header", example = "spcyQkKp9XJEl")
    })
    public ResponseData<Void> removeRoleMembers(@PathVariable("roleId") Long roleId, @RequestBody @Valid DeleteRoleMemberRo data) {
        String spaceId = LoginContext.me().getSpaceId();
        // check if space has the role.
        iRoleService.checkRoleExistBySpaceIdAndRoleId(spaceId, roleId,
                status -> ExceptionUtil.isTrue(status, NOT_EXIST_ROLE));
        // remove role members.
        List<Long> memberIds = iRoleMemberService.removeByRoleIdAndRoleMemberIds(roleId, data.getUnitIds());
        // send the message being removed to the role to the members.
        // ps: it no filter the role exist teams' member. so member may be notified repeatedly.
        NotificationRenderFieldHolder.set(NotificationRenderField.builder().playerIds(memberIds).bodyExtras(
                Dict.create().set(ROLE_NAME, iRoleService.getRoleNameByRoleId(roleId))).build());
        return ResponseData.success();
    }

    /**
     * 删除角色 - 完全删除指定的角色及其所有相关数据
     * 只有在角色下无任何成员时才能执行删除操作
     * 需要先移除所有角色成员后才能删除角色
     * 删除后角色相关的所有权限配置都将被清理
     * @param roleId 角色ID
     * @return 无返回数据，操作成功即可
     */
    @PostResource(path = "/roles/{roleId}", method = RequestMethod.DELETE, name = "delete role", tags = "DELETE_ROLE")
    @ApiOperation(value = "delete role", notes = "delete role", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams({
            @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", required = true, dataTypeClass = String.class, paramType = "header", example = "spcyQkKp9XJEl")
    })
    public ResponseData<Void> deleteRole(@PathVariable("roleId") Long roleId) {
        String spaceId = LoginContext.me().getSpaceId();
        // check if space has the role.
        iRoleService.checkRoleExistBySpaceIdAndRoleId(spaceId, roleId,
                status -> ExceptionUtil.isTrue(status, NOT_EXIST_ROLE));
        // check if role has role members.
        iRoleMemberService.checkRoleMemberExistByRoleId(roleId,
                status -> ExceptionUtil.isFalse(status, ROLE_EXIST_ROLE_MEMBER));
        // delete the role by role id.
        iRoleService.deleteRole(roleId);
        return ResponseData.success();
    }

    /**
     * 初始化空间角色 - 为新创建的空间初始化默认角色列表
     * 仅在空间尚无任何角色时才能执行初始化操作
     * 系统将创建一组标准的初始角色，方便空间管理和权限控制
     * @return 无返回数据，操作成功即可
     */
    @PostResource(path = "/roles/init", name = "create init role", tags = "CREATE_ROLE")
    @ApiOperation(value = "create init role", notes = "create init role", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams({
            @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", required = true, dataTypeClass = String.class, paramType = "header", example = "spcyQkKp9XJEl")
    })
    public ResponseData<Void> initRoles() {
        String spaceId = LoginContext.me().getSpaceId();
        // check if no exist roles in space.
        iRoleService.checkRoleExistBySpaceId(spaceId,
                status -> ExceptionUtil.isFalse(status, SPACE_EXIST_ROLES));
        Long userId = UserHolder.get();
        // init the space's role list.
        iRoleService.initRoleList(userId, spaceId);
        return ResponseData.success();
    }
}
