/*
 * APITable <https://github.com/apitable/apitable>
 * Copyright (C) 2022 APITable Ltd. <https://apitable.com>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.apitable.organization.controller;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Editor;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import com.apitable.organization.dto.SearchMemberDTO;
import com.apitable.organization.mapper.MemberMapper;
import com.apitable.organization.mapper.TeamMapper;
import com.apitable.organization.dto.LoadSearchDTO;
import com.apitable.organization.dto.MemberIsolatedInfo;
import com.apitable.organization.ro.SearchUnitRo;
import com.apitable.organization.service.IMemberSearchService;
import com.apitable.organization.service.IOrganizationService;
import com.apitable.organization.service.ITeamService;
import com.apitable.organization.vo.OrganizationUnitVo;
import com.apitable.organization.vo.SearchMemberResultVo;
import com.apitable.organization.vo.SearchResultVo;
import com.apitable.organization.vo.SearchTeamResultVo;
import com.apitable.organization.vo.SubUnitResultVo;
import com.apitable.organization.vo.UnitInfoVo;
import com.apitable.organization.vo.UnitSearchResultVo;
import com.apitable.shared.component.scanner.annotation.ApiResource;
import com.apitable.shared.component.scanner.annotation.GetResource;
import com.apitable.shared.component.scanner.annotation.PostResource;
import com.apitable.shared.config.properties.ConstProperties;
import com.apitable.shared.constants.ParamsConstants;
import com.apitable.shared.context.LoginContext;
import com.apitable.shared.context.SessionContext;
import com.apitable.workspace.enums.IdRulePrefixEnum;
import com.apitable.workspace.dto.NodeShareDTO;
import com.apitable.organization.service.IMemberService;
import com.apitable.space.service.ISpaceService;
import com.apitable.template.service.ITemplateService;
import com.apitable.workspace.mapper.NodeShareSettingMapper;
import com.apitable.shared.util.information.InformationUtil;
import com.apitable.space.enums.SpaceException;
import com.apitable.workspace.enums.NodeException;
import com.apitable.core.support.ResponseData;
import com.apitable.core.util.ExceptionUtil;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static java.util.stream.Collectors.toList;

/**
 * 组织架构控制器 - 提供组织架构的整体搜索、层级查询和统一管理功能
 * 包括全局搜索、组织单元查询、子单元加载、智能推荐等高级功能
 * 支持跨平台访问（分享链接、模板等）和权限隔离机制
 * 
 * Contact Organization Controller - Provides overall search, hierarchical query and unified management functions for organizational structure
 */
@RestController
@Api(tags = "Contact Organization Api")
@ApiResource(path = "/org")
@Slf4j
public class OrganizationController {

    @Resource
    private ISpaceService iSpaceService;

    @Resource
    private TeamMapper teamMapper;

    @Resource
    private IMemberService memberService;

    @Resource
    private IOrganizationService iOrganizationService;

    @Resource
    private NodeShareSettingMapper nodeShareSettingMapper;

    @Resource
    private ITemplateService iTemplateService;

    @Resource
    private ConstProperties constProperties;

    @Resource
    private MemberMapper memberMapper;

    @Resource
    private ITeamService iTeamService;

    @Resource
    private IMemberSearchService iMemberSearchService;

    /**
     * 全局搜索 - 在组织架构中同时搜索部门和成员
     * 支持对部门名称和成员姓名的模糊匹配搜索
     * 返回结果包含高亮显示的匹配关键词，提升用户体验
     * @param keyword 搜索关键词
     * @param className 高亮显示的CSS样式类名
     * @return 搜索结果，包含匹配的部门和成员信息
     */
    @GetResource(path = "/search", name = "Global search")
    @ApiOperation(value = "Global search", notes = "fuzzy search department or members", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams({
        @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", required = true, dataTypeClass = String.class, paramType = "header", example = "spcyQkKp9XJEl"),
        @ApiImplicitParam(name = "keyword", value = "keyword", required = true, dataTypeClass = String.class, paramType = "query", example = "design"),
        @ApiImplicitParam(name = "className", value = "the highlight style", dataTypeClass = String.class, paramType = "query", example = "highLight")
    })
    public ResponseData<SearchResultVo> searchTeamInfo(@RequestParam("keyword") String keyword,
            @RequestParam(value = "className", required = false, defaultValue = "highLight") String className) {
        String spaceId = LoginContext.me().getSpaceId();
        SearchResultVo result = new SearchResultVo();
        // fuzzy search department
        List<SearchTeamResultVo> teams = teamMapper.selectByTeamName(spaceId, keyword);
        if (CollUtil.isNotEmpty(teams)) {
            CollUtil.filter(teams, (Editor<SearchTeamResultVo>) vo -> {
                vo.setOriginName(vo.getTeamName());
                vo.setTeamName(InformationUtil.keywordHighlight(vo.getTeamName(), keyword, className));
                return vo;
            });
            result.setTeams(teams);
        }
        // fuzzy search members
        List<SearchMemberResultVo> searchMemberResultVos = iMemberSearchService.getByName(spaceId, keyword, className);
        result.setMembers(searchMemberResultVos);

        return ResponseData.success(result);
    }

    /**
     * 搜索部门和成员（即将废弃） - 统一搜索组织单元的旧版接口
     * 返回格式化的组织单元列表，包含部门和成员信息
     * 推荐使用新的全局搜索接口来替代此方法
     * @param keyword 搜索关键词
     * @param className 高亮显示的CSS样式类名
     * @return 统一格式的组织单元列表，包含部门和成员
     */
    @GetResource(path = "/search/unit", name = "search for departments or members")
    @ApiOperation(value = "Search departments or members（it will be abandoned）", notes = "fuzzy search unit", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams({
        @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", required = true, dataTypeClass = String.class, paramType = "header", example = "spcyQkKp9XJEl"),
        @ApiImplicitParam(name = "keyword", value = "keyword", required = true, dataTypeClass = String.class, paramType = "query", example = "design"),
        @ApiImplicitParam(name = "className", value = "the highlight style", dataTypeClass = String.class, paramType = "query", example = "highLight")
    })
    public ResponseData<List<OrganizationUnitVo>> searchSubTeamAndMembers(@RequestParam("keyword") String keyword,
            @RequestParam(value = "className", required = false, defaultValue = "highLight") String className) {
        String spaceId = LoginContext.me().getSpaceId();
        List<OrganizationUnitVo> resList = new ArrayList<>();
        // fuzzy search department
        List<SearchTeamResultVo> teams = teamMapper.selectByTeamName(spaceId, keyword);
        CollUtil.forEach(teams.iterator(), (team, index) -> {
            OrganizationUnitVo vo = new OrganizationUnitVo();
            vo.setId(team.getTeamId());
            vo.setOriginName(team.getTeamName());
            vo.setName(InformationUtil.keywordHighlight(team.getTeamName(), keyword, className));
            vo.setType(1);
            vo.setShortName(team.getShortName());
            vo.setMemberCount(team.getMemberCount());
            vo.setHasChildren(team.getHasChildren());
            resList.add(vo);
        });
        // fuzzy search members
        List<SearchMemberDTO> members = memberMapper.selectByName(spaceId, keyword);
        CollUtil.forEach(members.iterator(), (member, index) -> {
            OrganizationUnitVo vo = new OrganizationUnitVo();
            vo.setId(member.getMemberId());
            vo.setOriginName(member.getMemberName());
            vo.setName(InformationUtil.keywordHighlight(member.getMemberName(), keyword, className));
            vo.setType(2);
            vo.setAvatar(member.getAvatar());
            vo.setAvatarColor(member.getColor());
            vo.setNickName(member.getNickName());
            if (CollUtil.isNotEmpty(member.getTeam())) {
                List<String> teamNames = CollUtil.getFieldValues(member.getTeam(), "teamName", String.class);
                vo.setTeams(CollUtil.join(teamNames, "｜"));
            }
            vo.setIsActive(member.getIsActive());
            resList.add(vo);
        });
        return ResponseData.success(resList);
    }

    /**
     * 搜索组织资源 - 支持非登录用户通过分享链接访问的组织搜索
     * 支持群众分享节点和模板场景下的组织架构搜索
     * 按关键词模糊匹配组织单元，返回带有高亮显示的结果
     * @param keyword 搜索关键词
     * @param linkId 链接ID，可以是分享节点ID或模板ID
     * @param className 高亮显示的CSS样式类名
     * @return 组织单元搜索结果，包含部门和成员信息
     */
    @GetResource(path = "/searchUnit", requiredLogin = false)
    @ApiOperation(value = "search organization resources", notes = "Provide input word fuzzy search organization resources")
    @ApiImplicitParams({
        @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", dataTypeClass = String.class, paramType = "header", example = "spcyQkKp9XJEl"),
        @ApiImplicitParam(name = "linkId", value = "link id: node share id | template id", dataTypeClass = String.class, paramType = "query", example = "shr8T8vAfehg3yj3McmDG"),
        @ApiImplicitParam(name = "className", value = "the highlight style", dataTypeClass = String.class, paramType = "query", example = "highLight"),
        @ApiImplicitParam(name = "keyword", value = "keyword", required = true, dataTypeClass = String.class, paramType = "query", example = "design")
    })
    public ResponseData<UnitSearchResultVo> search(@RequestParam(name = "keyword") String keyword,
            @RequestParam(value = "linkId", required = false) String linkId,
            @RequestParam(value = "className", required = false, defaultValue = "highLight") String className) {

        String spaceId = this.getSpaceId(linkId);
        UnitSearchResultVo vo = iOrganizationService.findLikeUnitName(spaceId, keyword, className);

        return ResponseData.success(vo);
    }

    /**
     * 查询部门的子部门和成员 - 获取指定部门下的直接子部门和成员列表
     * 支持非登录用户通过分享链接访问，包含权限隔离机制
     * 当teamId为0时表示查询根部门，支持成员视图隔离功能
     * @param teamId 部门ID，默认为0表示根部门
     * @param linkId 链接ID，用于分享访问场景
     * @return 子部门和成员的统一结果集
     */
    @GetResource(path = "/getSubUnitList", requiredLogin = false)
    @ApiOperation(value = "Query the sub departments and members of department", notes = "Query the sub departments and members of department. if team id lack, default is 0", produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiImplicitParams({
        @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", dataTypeClass = String.class, paramType = "header", example = "spcyQkKp9XJEl"),
        @ApiImplicitParam(name = "teamId", value = "team id", defaultValue = "0", dataTypeClass = String.class, paramType = "query", example = "0"),
        @ApiImplicitParam(name = "linkId", value = "link id: node share id | template id", dataTypeClass = String.class, paramType = "query", example = "shr8T8vAfehg3yj3McmDG")
    })
    public ResponseData<SubUnitResultVo> getSubUnitList(@RequestParam(name = "teamId", required = false, defaultValue = "0") Long teamId,
            @RequestParam(value = "linkId", required = false) String linkId) {
        // get the link id's space
        String spaceId = this.getSpaceId(linkId);
        Long memberId = LoginContext.me().getMemberId();
        // determine whether the team id is 0
        if (teamId == 0) {
            // check whether members are isolated from contacts
            MemberIsolatedInfo memberIsolatedInfo = iTeamService.checkMemberIsolatedBySpaceId(spaceId, memberId);
            if (Boolean.TRUE.equals(memberIsolatedInfo.isIsolated())) {
                // Load the first-layer department in the member's department
                SubUnitResultVo resultVo = iOrganizationService.loadMemberFirstTeams(spaceId, memberIsolatedInfo.getTeamIds());
                return ResponseData.success(resultVo);
            }
        }
        // load the default return value
        SubUnitResultVo subUnitResultVo = iOrganizationService.findSubUnit(spaceId, teamId);
        return ResponseData.success(subUnitResultVo);
    }

    /**
     * 加载/搜索部门和成员 - 智能加载组织单元，支持多种加载策略
     * 无关键词时加载最近选择的单元，无选择时加载同组最近添加的成员
     * 支持关键词搜索、单元过滤、邮箱搜索等高级功能
     * 最多加载10条记录，提供高效的用户体验
     * @param params 加载搜索参数，包含关键词、过滤条件等
     * @return 组织单元信息列表，排除已删除的单元
     */
    @GetResource(path = "/loadOrSearch", requiredLogin = false)
    @ApiOperation(value = "Load/search departments and members", notes = "The most recently selected units are loaded by default when not keyword. The most recently added member of the same group are loaded when not selected. Load max 10")
    @ApiImplicitParams({
        @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", dataTypeClass = String.class, paramType = "header", example = "spczJrh2i3tLW"),
        @ApiImplicitParam(name = "linkId", value = "link id: node share id | template id", dataTypeClass = String.class, paramType = "query", example = "shr8T8vAfehg3yj3McmDG"),
        @ApiImplicitParam(name = "keyword", value = "keyword", dataTypeClass = String.class, paramType = "query", example = "Lili"),
        @ApiImplicitParam(name = "unitIds", value = "unitIds", dataTypeClass = String.class, paramType = "query", example = "1271,1272"),
        @ApiImplicitParam(name = "filterIds", value = "specifies the organizational unit to filter", dataTypeClass = String.class, paramType = "query", example = "123,124"),
        @ApiImplicitParam(name = "all", value = "whether to load all departments and members", defaultValue = "false", dataTypeClass = Boolean.class, paramType = "query"),
        @ApiImplicitParam(name = "searchEmail", value = "whether to search for emails", defaultValue = "false", dataTypeClass = Boolean.class, paramType = "query")
    })
    public ResponseData<List<UnitInfoVo>> loadOrSearch(@Valid LoadSearchDTO params) {
        // sharing node/template: un login users invoke processing
        Long userId = null;
        String spaceId;
        Long sharer = null;
        String linkId = params.getLinkId();
        if (StrUtil.isBlank(linkId)) {
            userId = SessionContext.getUserId();
            spaceId = LoginContext.me().getSpaceId();
        }
        else if (linkId.startsWith(IdRulePrefixEnum.SHARE.getIdRulePrefixEnum())) {
            // the sharing nodes
            NodeShareDTO nodeShare = nodeShareSettingMapper.selectDtoByShareId(linkId);
            ExceptionUtil.isNotNull(nodeShare, NodeException.SHARE_EXPIRE);
            spaceId = nodeShare.getSpaceId();
            sharer = nodeShare.getOperator();
        }
        else {
            // the template
            String templateSpaceId = iTemplateService.getSpaceId(linkId);
            // requirements are official templates, or user in the space
            if (!constProperties.getTemplateSpace().contains(templateSpaceId)) {
                userId = SessionContext.getUserId();
                Long memberId = memberMapper.selectIdByUserIdAndSpaceId(userId, templateSpaceId);
                ExceptionUtil.isNotNull(memberId, SpaceException.NOT_IN_SPACE);
            }
            spaceId = templateSpaceId;
        }
        List<UnitInfoVo> vos = iOrganizationService.loadOrSearchInfo(userId, spaceId, params, sharer);
        List<UnitInfoVo> existUnitInfo = vos.stream().filter(unitInfoVo -> !unitInfoVo.getIsDeleted()).collect(toList());
        return ResponseData.success(existUnitInfo);
    }

    /**
     * 精确查询部门和成员 - 根据名称列表精确匹配组织单元
     * 适用于字段转换场景，当GET请求数据量过大超出限制时使用
     * 支持非登录用户通过分享链接访问，提供高性能的批量查询
     * @param ro 精确搜索请求，包含名称列表和链接信息
     * @return 匹配的组织单元信息列表
     */
    @PostResource(path = "/searchUnitInfoVo", requiredLogin = false)
    @ApiOperation(value = "accurately query departments and members", notes = "scenario:field conversion（If the amount of data is large, the content requested by GET will exceed the limit.）")
    @ApiImplicitParam(name = ParamsConstants.SPACE_ID, value = "space id", dataTypeClass = String.class, paramType = "header", example = "spczJrh2i3tLW")
    public ResponseData<List<UnitInfoVo>> searchUnitInfoVo(@RequestBody @Valid SearchUnitRo ro) {
        String spaceId = this.getSpaceId(ro.getLinkId());
        List<UnitInfoVo> vos = iOrganizationService.accurateSearch(spaceId, StrUtil.splitTrim(ro.getNames(), ','));
        return ResponseData.success(vos);
    }

    /**
     * 获取空间ID - 根据链接ID获取对应的空间ID
     * 支持分享节点和模板的空间ID解析，用于跨平台访问
     * @param linkId 链接ID，可以是分享节点ID或模板ID
     * @return 对应的空间ID
     */
    private String getSpaceId(String linkId) {
        if (StrUtil.isBlank(linkId)) {
            return LoginContext.me().getSpaceId();
        }
        // non-official website access
        return iSpaceService.getSpaceIdByLinkId(linkId);
    }
}
