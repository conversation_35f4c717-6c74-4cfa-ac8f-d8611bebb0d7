/*
 * APITable <https://github.com/apitable/apitable>
 * Copyright (C) 2022 APITable Ltd. <https://apitable.com>
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
 */

package com.apitable.asset.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import com.apitable.core.exception.BusinessException;
import com.apitable.core.support.serializer.IBaseEnum;

/**
 * asset upload scope
 *
 * <AUTHOR>
 */
@Deprecated
@Getter
@AllArgsConstructor
public enum AssetUploadScope implements IBaseEnum {

    /**
     * single file
     * bucket:key，Indicates that only users are allowed to upload files with the specified key
     */
    SINGLE(0),

    /**
     * multiple files
     * bucket:keyPrefix，Indicates that users are allowed to upload files prefixed with the scope's keyPrefix.
     */
    MULTIPLE(1);

    private final Integer value;

    public static AssetUploadScope of(Integer value) {
        for (AssetUploadScope type : AssetUploadScope.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new BusinessException("Unknown Upload Scope!");
    }

}
